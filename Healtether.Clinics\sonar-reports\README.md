# SonarQube Reports for Healtether Clinics API

This directory contains SonarQube analysis reports for the Healtether Clinics API project.

## Generated Files

- `sonarqube-report-healtether-clinics-api.json` - Detailed analysis report with all issues, metrics, and quality gate status

## Usage

Run SonarQube analysis and generate reports:

```bash
# Run SonarQube scan
npm run sonar

# Generate detailed API report
node ../sonarqube-api-client.js healtether-clinics-api
```

## Report Contents

The generated reports include:
- Quality Gate status and conditions
- Security issues and hotspots
- Code smells and maintainability issues
- Test coverage metrics
- Code duplication analysis
- Detailed issue breakdown with file locations and line numbers
