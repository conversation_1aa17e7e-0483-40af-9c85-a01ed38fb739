import { google } from 'googleapis';

// Secure credential configuration using environment variables
const ADMIN_EMAIL = process.env.GOOGLE_MEET_ADMIN_EMAIL;

// Create service account credentials from environment variables
const getServiceAccountCredentials = () => {
    return {
        type: "service_account",
        project_id: process.env.GOOGLE_CLOUD_PROJECT_ID,
        private_key_id: process.env.GOOGLE_CLOUD_PRIVATE_KEY_ID,
        private_key: process.env.GOOGLE_CLOUD_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        client_email: process.env.GOOGLE_CLOUD_CLIENT_EMAIL,
        client_id: process.env.GOOGLE_CLOUD_CLIENT_ID,
        auth_uri: "https://accounts.google.com/o/oauth2/auth",
        token_uri: "https://oauth2.googleapis.com/token",
        auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
        client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${encodeURIComponent(process.env.GOOGLE_CLOUD_CLIENT_EMAIL || '')}`,
        universe_domain: "googleapis.com"
    };
};

let calendarClient;

async function initGoogleCalendarClient() {
    try {
        const SCOPES = ['https://www.googleapis.com/auth/calendar'];
        const auth = new google.auth.GoogleAuth({
            credentials: getServiceAccountCredentials(),
            scopes: SCOPES,
        });
        calendarClient = await auth.getClient();
    } catch (error) {
        console.error('Error initializing Google Calendar client:', error);
        throw error;
    }
}

async function getClientDelegation() {
    try {
        const SCOPES = 'https://www.googleapis.com/auth/calendar';
        const auth = new google.auth.GoogleAuth({
            credentials: getServiceAccountCredentials(),
            scopes: SCOPES,
        });
        const authClient = await auth.getClient();
        const delegatedAuth = await authClient.createScoped(SCOPES);
        delegatedAuth.subject = ADMIN_EMAIL;
        return google.calendar({ version: 'v3', auth: delegatedAuth });
    } catch (error) {
        console.error('Error in getClientDelegation:', error);
        throw error;
    }
}

export { initGoogleCalendarClient, getClientDelegation };