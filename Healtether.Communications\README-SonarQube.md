# SonarQube Analysis Guide

This guide provides step-by-step instructions for running SonarQube analysis on the Healtether Communications API project.

## Prerequisites

- **Java 17+** installed and configured
- **Node.js** and **npm** installed
- **SonarQube Server** running locally on port 9000
- **SonarQube Scanner** installed and configured

## Quick Start

### 1. Open SonarQube Dashboard

```bash
# Open the SonarQube dashboard in your browser
http://localhost:9000/dashboard?id=healtether-communications-api
userame: admin
password: Healtether__1
```

Or use the direct project URL after running a scan.

### 2. Run SonarQube Scan

Navigate to the project directory and run:

```bash
cd Healtether.Communications
npm run sonar
```

This command will:
- Execute the SonarQube scanner
- Analyze code quality, bugs, vulnerabilities, and code smells
- Calculate test coverage from existing coverage reports
- Upload results to the SonarQube server

### 3. Run API Analysis (Detailed Report)

After running the scan, get detailed programmatic results:

```bash
cd Healtether.Communications
node ../sonarqube-api-client.js
```

This will:
- Fetch detailed analysis data via SonarQube API
- Generate comprehensive JSON reports
- Save reports to `sonar-reports/` directory
- Display formatted results in terminal

## Project Configuration

### SonarQube Project Settings

- **Project Key**: `healtether-communications-api`
- **Project Name**: Healtether Communications API
- **Authentication Token**: `sqp_0e576c502636aec9dd0b2e1ebba6f68ce1a948f6`

### Quality Gate Conditions

The project must meet these conditions to pass:

1. **Test Coverage**: ≥80% on new code
2. **Duplicated Lines**: <3% on new code  
3. **New Violations**: 0 new issues

## Current Status

### Latest Metrics (as of last scan)
- **Overall Coverage**: 18.7%
- **New Code Coverage**: 52.3% (needs 80%)
- **Bugs**: 2 (down from 11)
- **Code Smells**: 409 (down from 480)
- **Security Rating**: A ✅
- **Quality Gate**: ❌ FAILED (coverage threshold)

### Critical Issues
- **1 BLOCKER**: Infinite loop in `utils/common.js`
- **Coverage Gap**: Need 27.7% more coverage on new code

## File Structure

```
Healtether.Communications/
├── sonar-project.properties     # SonarQube configuration
├── coverage/                    # Jest coverage reports
│   └── lcov.info               # Coverage data for SonarQube
├── sonar-reports/              # Generated analysis reports
│   └── sonarqube-report-*.json # Detailed API results
└── __tests__/                  # Test files
    ├── controllers/            # Controller tests
    ├── helper/                 # Helper function tests
    └── utils/                  # Utility tests
```

## Troubleshooting

### Common Issues

1. **Quality Gate Failed**
   - Check coverage percentage in dashboard
   - Run more tests to increase coverage
   - Fix critical/blocker issues first

2. **Scanner Fails**
   - Ensure Java 17+ is installed
   - Check SonarQube server is running on port 9000
   - Verify authentication token is valid

3. **No Coverage Data**
   - Run tests first: `npm test`
   - Ensure `coverage/lcov.info` exists
   - Check Jest configuration includes coverage

4. **API Client Fails**
   - Verify SonarQube server is accessible
   - Check authentication token permissions
   - Ensure project exists and scan completed

## Commands Reference

### Essential Commands

```bash
# Run tests with coverage
npm test

# Run SonarQube scan
npm run sonar

# Get detailed API results
node ../sonarqube-api-client.js

# Open dashboard
http://localhost:9000/dashboard?id=healtether-communications-api
```

### Advanced Commands

```bash
# Run specific test file
npm test -- __tests__/controllers/message.controller.spec.js

# Run tests with verbose output
npm test -- --verbose

# Generate coverage report only
npm test -- --coverage --watchAll=false

# Run scanner with debug output
sonar-scanner -Dsonar.verbose=true
```

## Integration Workflow

### Recommended Development Cycle

1. **Write/Update Code**
2. **Write/Update Tests** → `npm test`
3. **Run SonarQube Scan** → `npm run sonar`
4. **Check Dashboard** → Review issues and coverage
5. **Get Detailed Report** → `node ../sonarqube-api-client.js`
6. **Fix Issues** → Address bugs, code smells, coverage gaps
7. **Repeat** until Quality Gate passes ✅

### Quality Gate Success Criteria

- ✅ **Coverage**: ≥80% on new code
- ✅ **Duplicated Lines**: <3% on new code
- ✅ **New Violations**: 0 new issues
- ✅ **Security Rating**: A
- ✅ **Reliability Rating**: A or B

## Support

For issues with SonarQube analysis:
1. Check the SonarQube server logs
2. Verify project configuration in `sonar-project.properties`
3. Ensure all prerequisites are installed and configured
4. Review the generated reports in `sonar-reports/` directory

---

**Last Updated**: 2024-12-20  
**SonarQube Version**: 25.6.0  
**Scanner Version**: 4.3.0
