# SonarQube Configuration for Healtether Clinics API
sonar.projectKey=healtether-clinics-api
sonar.projectName=Healtether Clinics API
sonar.projectVersion=1.0.0

# Source code configuration
sonar.sources=.
sonar.exclusions=**/node_modules/**,**/coverage/**,**/dist/**,**/build/**,**/*.min.js,**/vendor/**,**/third-party/**,**/*.log,**/logs/**,**/temp/**,**/tmp/**,**/*test.js,**/*spec.js,**/*.test.js,**/*.spec.js

# Test configuration
sonar.tests=__tests__
sonar.test.inclusions=**/*test.js,**/*spec.js,**/*.test.js,**/*.spec.js
sonar.test.exclusions=node_modules/**

# Coverage configuration
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*test.js,**/*spec.js,**/*.test.js,**/*.spec.js,**/node_modules/**

# Duplication exclusions
sonar.cpd.exclusions=**/*test.js,**/*spec.js

# Language configuration
sonar.language=js

# SonarQube server configuration
sonar.host.url=http://localhost:9000
sonar.token=sqp_9833dac32acc6233060116e797a16d7c314fcf5c

# Quality gate configuration
sonar.qualitygate.wait=true
